version: 2.1
jobs:
  test:
    docker:
      - image: circleci/node:11.5.0
    working_directory: ~/repo
    steps:
      - checkout

      - restore_cache:
          keys:
            - v1-dependencies-{{ checksum "package.json" }}
            - v1-dependencies-
      - run: yarn install
      - save_cache:
          paths:
            - node_modules
          key: v1-dependencies-{{ checksum "package.json" }}
      - run: yarn test

  release:
    docker:
      - image: cimg/node:18.12.1
    steps:
      - checkout
      - restore_cache:
          keys:
            - v1-dependencies-{{ checksum "package.json" }}
            - v1-dependencies-
      - run: yarn install
      - run: npx semantic-release

workflows:
  version: 2.1
  test_and_release:
    jobs:
      - test
      - release:
          context:
            - test-runner:npm-release
          requires:
            - test
          filters:
            branches:
              only:
                - master